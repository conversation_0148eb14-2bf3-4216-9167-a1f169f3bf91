"""Test utilities for generating synthetic TFRecord files and test data.

This module provides utilities for creating test TFRecord files with synthetic
radar spectra data for testing the data ingestion pipeline.
"""

from __future__ import annotations

import json
from pathlib import Path
from typing import List, Optional, Tuple

import numpy as np
import tensorflow as tf


def create_synthetic_tfrecord(
    output_path: Path,
    class_name: str,
    n_samples: int = 50,
    height: int = 3,
    width: int = 3,
    base_value: float = 100.0,
    noise_std: float = 10.0,
    seed: Optional[int] = None
) -> List[str]:
    """Create a synthetic TFRecord file with test data.
    
    Args:
        output_path: Path where the TFRecord file will be created.
        class_name: Class label for all samples (e.g., "A" or "B").
        n_samples: Number of samples to generate.
        height: Height of synthetic spectra arrays.
        width: Width of synthetic spectra arrays.
        base_value: Base value for synthetic spectra (different for each class).
        noise_std: Standard deviation of Gaussian noise added to spectra.
        seed: Random seed for reproducible generation.
        
    Returns:
        List of plot_ids that were generated.
        
    Example:
        >>> plot_ids = create_synthetic_tfrecord(
        ...     Path("test_A.tfrecord"), "A", n_samples=10, seed=42
        ... )
        >>> len(plot_ids)
        10
    """
    if seed is not None:
        np.random.seed(seed)
        
    output_path.parent.mkdir(parents=True, exist_ok=True)
    plot_ids = []
    
    with tf.io.TFRecordWriter(str(output_path)) as writer:
        for i in range(n_samples):
            plot_id = f"{class_name}{i:03d}"
            plot_ids.append(plot_id)
            
            # Create synthetic spectra with class-specific base value + noise
            spectra = (base_value + np.random.normal(0, noise_std, (height, width))).astype(np.float32)
            
            # Create TFRecord example
            feature = {
                "name": tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[b"synthetic_recording"])
                ),
                "spectra": tf.train.Feature(
                    bytes_list=tf.train.BytesList(
                        value=[tf.io.serialize_tensor(tf.convert_to_tensor(spectra)).numpy()]
                    )
                ),
                "plot_id": tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[plot_id.encode("utf-8")])
                ),
                "class": tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[class_name.encode("utf-8")])
                ),
                "subclass": tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[f"{class_name}_sub".encode("utf-8")])
                ),
                "snr": tf.train.Feature(
                    float_list=tf.train.FloatList(value=[20.0])
                ),
            }
            
            example = tf.train.Example(features=tf.train.Features(feature=feature))
            writer.write(example.SerializeToString())
    
    return plot_ids


def create_test_tfrecord_pair(
    temp_dir: Path,
    n_samples_per_class: int = 50,
    height: int = 3,
    width: int = 3,
    seed: Optional[int] = None
) -> Tuple[Path, Path, List[str], List[str]]:
    """Create a pair of test TFRecord files for classes A and B.
    
    Args:
        temp_dir: Directory to create the TFRecord files in.
        n_samples_per_class: Number of samples per class.
        height: Height of synthetic spectra arrays.
        width: Width of synthetic spectra arrays.
        seed: Random seed for reproducible generation.
        
    Returns:
        Tuple of (path_A, path_B, plot_ids_A, plot_ids_B).
    """
    path_a = temp_dir / "A.tfrecord"
    path_b = temp_dir / "B.tfrecord"
    
    # Use different base values to make classes distinguishable
    plot_ids_a = create_synthetic_tfrecord(
        path_a, "A", n_samples_per_class, height, width, 
        base_value=100.0, seed=seed
    )
    plot_ids_b = create_synthetic_tfrecord(
        path_b, "B", n_samples_per_class, height, width, 
        base_value=200.0, seed=seed + 1 if seed is not None else None
    )
    
    return path_a, path_b, plot_ids_a, plot_ids_b


def create_allow_list_json(
    output_path: Path,
    plot_ids: List[str],
    format_type: str = "object"
) -> None:
    """Create a JSON allow-list file for filtering tests.
    
    Args:
        output_path: Path where the JSON file will be created.
        plot_ids: List of plot_ids to include in the allow-list.
        format_type: Format type - "object" or "list".
        
    Example:
        >>> create_allow_list_json(
        ...     Path("allow_list.json"), ["A001", "A002", "B001"], "object"
        ... )
    """
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    if format_type == "object":
        data = {"valid_plot_ids": plot_ids}
    elif format_type == "list":
        data = plot_ids
    else:
        raise ValueError(f"Invalid format_type: {format_type}")
    
    with output_path.open('w', encoding='utf-8') as f:
        json.dump(data, f, indent=2)


def create_constant_spectra_tfrecord(
    output_path: Path,
    class_name: str,
    constant_value: float = 128.0,
    n_samples: int = 10,
    height: int = 3,
    width: int = 3
) -> List[str]:
    """Create TFRecord with constant spectra values for normalization testing.
    
    This creates spectra with all pixels having the same value, useful for
    testing zero-variance normalization handling.
    
    Args:
        output_path: Path where the TFRecord file will be created.
        class_name: Class label for all samples.
        constant_value: Constant value for all pixels in all spectra.
        n_samples: Number of samples to generate.
        height: Height of spectra arrays.
        width: Width of spectra arrays.
        
    Returns:
        List of plot_ids that were generated.
    """
    output_path.parent.mkdir(parents=True, exist_ok=True)
    plot_ids = []
    
    with tf.io.TFRecordWriter(str(output_path)) as writer:
        for i in range(n_samples):
            plot_id = f"{class_name}{i:03d}"
            plot_ids.append(plot_id)
            
            # Create constant spectra
            spectra = np.full((height, width), constant_value, dtype=np.float32)
            
            # Create TFRecord example
            feature = {
                "name": tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[b"constant_recording"])
                ),
                "spectra": tf.train.Feature(
                    bytes_list=tf.train.BytesList(
                        value=[tf.io.serialize_tensor(tf.convert_to_tensor(spectra)).numpy()]
                    )
                ),
                "plot_id": tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[plot_id.encode("utf-8")])
                ),
                "class": tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[class_name.encode("utf-8")])
                ),
                "subclass": tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[f"{class_name}_sub".encode("utf-8")])
                ),
                "snr": tf.train.Feature(
                    float_list=tf.train.FloatList(value=[20.0])
                ),
            }
            
            example = tf.train.Example(features=tf.train.Features(feature=feature))
            writer.write(example.SerializeToString())
    
    return plot_ids


def extract_class_sequence(dataset, max_samples: int = 100) -> List[str]:
    """Extract the sequence of class labels from a dataset.
    
    Utility function to extract class labels in order for testing shuffle mixing.
    
    Args:
        dataset: Dataset to extract from (should return metadata).
        max_samples: Maximum number of samples to extract.
        
    Returns:
        List of class labels in the order they appear.
    """
    classes = []
    for i, (_, _, meta) in enumerate(dataset):
        if i >= max_samples:
            break
        classes.append(meta["class"])
    return classes


def count_class_transitions(class_sequence: List[str]) -> int:
    """Count the number of class transitions in a sequence.
    
    Args:
        class_sequence: List of class labels in order.
        
    Returns:
        Number of times the class changes from one sample to the next.
        
    Example:
        >>> count_class_transitions(["A", "A", "B", "A", "B", "B"])
        3
    """
    if len(class_sequence) < 2:
        return 0
    
    transitions = 0
    for i in range(1, len(class_sequence)):
        if class_sequence[i] != class_sequence[i-1]:
            transitions += 1
    
    return transitions
