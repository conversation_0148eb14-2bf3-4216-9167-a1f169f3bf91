["tests/test_data_tfrecord.py::TestClassMixingAndShuffle::test_both_classes_appear_early", "tests/test_data_tfrecord.py::TestClassMixingAndShuffle::test_interleaving_effectiveness", "tests/test_data_tfrecord.py::TestClassMixingAndShuffle::test_shuffle_buffer_effect[0]", "tests/test_data_tfrecord.py::TestClassMixingAndShuffle::test_shuffle_buffer_effect[200]", "tests/test_data_tfrecord.py::TestClassMixingAndShuffle::test_shuffle_buffer_effect[500]", "tests/test_data_tfrecord.py::TestClassMixingAndShuffle::test_shuffle_buffer_effect[50]", "tests/test_data_tfrecord.py::TestClassMixingAndShuffle::test_shuffle_vs_no_shuffle", "tests/test_data_tfrecord.py::TestDeterminism::test_dataloader_factory_determinism", "tests/test_data_tfrecord.py::TestDeterminism::test_different_seeds_different_order", "tests/test_data_tfrecord.py::TestDeterminism::test_no_seed_is_non_deterministic", "tests/test_data_tfrecord.py::TestDeterminism::test_same_seed_same_order", "tests/test_data_tfrecord.py::TestFiltering::test_direct_list_filtering", "tests/test_data_tfrecord.py::TestFiltering::test_empty_allow_list", "tests/test_data_tfrecord.py::TestFiltering::test_filtering_preserves_class_distribution", "tests/test_data_tfrecord.py::TestFiltering::test_invalid_json_file_handling", "tests/test_data_tfrecord.py::TestFiltering::test_no_filtering_includes_all", "tests/test_data_tfrecord.py::TestFiltering::test_object_format_filtering", "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_empty_worker_handling", "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_multiworker_no_duplicates[2]", "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_multiworker_no_duplicates[3]", "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_single_worker_baseline", "tests/test_data_tfrecord.py::TestMultiWorkerSharding::test_worker_file_distribution", "tests/test_data_tfrecord.py::TestNormalization::test_global_normalization", "tests/test_data_tfrecord.py::TestNormalization::test_normalization_consistency", "tests/test_data_tfrecord.py::TestNormalization::test_normalization_preserves_shape[global]", "tests/test_data_tfrecord.py::TestNormalization::test_normalization_preserves_shape[per_sample]", "tests/test_data_tfrecord.py::TestNormalization::test_per_sample_normalization", "tests/test_data_tfrecord.py::TestNormalization::test_zero_variance_handling"]