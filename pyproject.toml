[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "tfrecord-pytorch-pipeline"
version = "1.0.0"
description = "Production-grade TFRecord → PyTorch data ingestion pipeline for radar spectra"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "Data Pipeline Team", email = "<EMAIL>"},
]
keywords = ["tensorflow", "pytorch", "tfrecord", "data-pipeline", "radar", "deep-learning"]
classifiers = [
    "Development Status :: 5 - Production/Stable",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    "torch>=2.0.0",
    "tensorflow>=2.12.0",
    "numpy>=1.21.0",
    "pyyaml>=6.0",
]

[project.optional-dependencies]
test = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-xdist>=3.0.0",
    "pytest-mock>=3.10.0",
    "pytest-benchmark>=4.0.0",
    "pytest-timeout>=2.1.0",
]
lint = [
    "flake8>=6.0.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]
dev = [
    "tfrecord-pytorch-pipeline[test,lint]",
    "jupyter>=1.0.0",
    "matplotlib>=3.5.0",
]

[project.urls]
Homepage = "https://github.com/example/tfrecord-pytorch-pipeline"
Documentation = "https://tfrecord-pytorch-pipeline.readthedocs.io/"
Repository = "https://github.com/example/tfrecord-pytorch-pipeline.git"
"Bug Tracker" = "https://github.com/example/tfrecord-pytorch-pipeline/issues"

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.setuptools.package-dir]
"" = "."

# Pytest configuration
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--tb=short",
    "--strict-markers",
    "--strict-config",
    "--color=yes",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "multiworker: marks tests that use multiple DataLoader workers",
]
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
]
timeout = 300  # 5 minutes per test

# Coverage configuration
[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__init__.py",
]
branch = true

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
ignore_errors = true
show_missing = true
precision = 2

[tool.coverage.html]
directory = "htmlcov"

# Black code formatting
[tool.black]
line-length = 100
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort import sorting
[tool.isort]
profile = "black"
line_length = 100
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
src_paths = ["src", "tests"]

# MyPy type checking
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
show_error_codes = true

[[tool.mypy.overrides]]
module = [
    "tensorflow.*",
    "torch.*",
    "numpy.*",
]
ignore_missing_imports = true

# Flake8 linting (in setup.cfg since flake8 doesn't support pyproject.toml yet)
# See setup.cfg for flake8 configuration
