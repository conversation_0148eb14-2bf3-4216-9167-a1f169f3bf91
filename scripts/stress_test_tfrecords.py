#!/usr/bin/env python3
"""Stress test script for TFRecord → PyTorch data ingestion pipeline.

This script generates large dummy TFRecord files and stress-tests the existing
data loader to verify proper interleaving, shuffling, and class mixing.
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import List, Dict, Any
import time

import numpy as np
import tensorflow as tf
import torch
from torch.utils.data import DataLoader

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))
from src.data.tfrecord_dataset import SpectraTFRecordDataset, make_train_loader

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_dummy_tfrecord(
    output_path: Path,
    class_name: str,
    n_samples: int,
    height: int,
    width: int,
    seed: int
) -> List[str]:
    """Create a TFRecord file with dummy radar spectra data.
    
    Args:
        output_path: Path where the TFRecord file will be created.
        class_name: Class label for all samples in this file.
        n_samples: Number of samples to generate.
        height: Height of spectra arrays.
        width: Width of spectra arrays.
        seed: Random seed for reproducible generation.
        
    Returns:
        List of plot_ids that were generated.
    """
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Set random seed for reproducible generation
    np.random.seed(seed)
    
    plot_ids = []
    
    logger.info(f"Creating {output_path} with {n_samples} samples of class '{class_name}'")
    
    with tf.io.TFRecordWriter(str(output_path)) as writer:
        for i in range(n_samples):
            # Generate synthetic spectra data
            spectra = np.random.uniform(0, 255, size=(height, width)).astype(np.float32)
            
            # Generate metadata
            plot_id = f"{class_name}_{i:06d}"
            plot_ids.append(plot_id)
            
            # Create TF Example
            feature = {
                'spectra': tf.train.Feature(
                    bytes_list=tf.train.BytesList(
                        value=[tf.io.serialize_tensor(spectra).numpy()]
                    )
                ),
                'plot_id': tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[plot_id.encode('utf-8')])
                ),
                'class': tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[class_name.encode('utf-8')])
                ),
                'subclass': tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[f"sub_{class_name}".encode('utf-8')])
                ),
                'snr': tf.train.Feature(
                    float_list=tf.train.FloatList(value=[np.random.uniform(10, 30)])
                ),
                'name': tf.train.Feature(
                    bytes_list=tf.train.BytesList(value=[f"recording_{plot_id}".encode('utf-8')])
                ),
            }
            
            example = tf.train.Example(features=tf.train.Features(feature=feature))
            writer.write(example.SerializeToString())
    
    logger.info(f"Created {output_path} with {len(plot_ids)} samples")
    return plot_ids


def generate_test_data(
    output_dir: Path,
    num_files: int,
    samples_per_file: int,
    height: int,
    width: int,
    classes: List[str],
    seed: int
) -> Dict[str, Any]:
    """Generate test TFRecord files for stress testing.
    
    Args:
        output_dir: Directory to create TFRecord files in.
        num_files: Number of files to create.
        samples_per_file: Number of samples per file.
        height: Height of spectra arrays.
        width: Width of spectra arrays.
        classes: List of class names.
        seed: Random seed for reproducible generation.
        
    Returns:
        Dictionary with file paths and metadata.
    """
    output_dir.mkdir(parents=True, exist_ok=True)
    
    files = []
    all_plot_ids = []
    class_to_idx = {cls: idx for idx, cls in enumerate(classes)}
    
    # Create files, cycling through classes
    for file_idx in range(num_files):
        class_name = classes[file_idx % len(classes)]
        filename = f"{class_name}_{file_idx:03d}.tfrecord"
        file_path = output_dir / filename
        
        plot_ids = create_dummy_tfrecord(
            file_path, class_name, samples_per_file, height, width, seed + file_idx
        )
        
        files.append(str(file_path))
        all_plot_ids.extend(plot_ids)
    
    return {
        "files": files,
        "class_to_idx": class_to_idx,
        "all_plot_ids": all_plot_ids,
        "total_samples": len(all_plot_ids)
    }


def stress_test_loader(
    files: List[str],
    class_to_idx: Dict[str, int],
    batch_size: int,
    num_workers: int,
    shuffle_buffer: int,
    interleave_block: int,
    allow_list_path: str = None
) -> None:
    """Stress test the data loader with generated files.
    
    Args:
        files: List of TFRecord file paths.
        class_to_idx: Mapping from class names to indices.
        batch_size: Batch size for DataLoader.
        num_workers: Number of DataLoader workers.
        shuffle_buffer: Shuffle buffer size.
        interleave_block: Interleave block length.
        allow_list_path: Optional path to allow-list JSON file.
    """
    logger.info("Starting stress test of data loader")
    logger.info(f"Files: {len(files)}")
    logger.info(f"Classes: {list(class_to_idx.keys())}")
    logger.info(f"Batch size: {batch_size}, Workers: {num_workers}")
    logger.info(f"Shuffle buffer: {shuffle_buffer}, Interleave block: {interleave_block}")
    
    # Create dataset
    dataset = SpectraTFRecordDataset(
        files=files,
        class_to_idx=class_to_idx,
        shuffle_buffer=shuffle_buffer,
        interleave_cycle=len(files),
        block_length=interleave_block,
        seed=42,
        allow_list_json=allow_list_path,
        return_meta=True
    )
    
    # Create DataLoader
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        num_workers=num_workers,
        drop_last=False,
        pin_memory=False
    )
    
    # Test iteration and collect statistics
    start_time = time.time()
    batch_count = 0
    sample_count = 0
    class_counts = {cls: 0 for cls in class_to_idx.keys()}
    first_batch_classes = []
    
    try:
        for batch_idx, batch in enumerate(dataloader):
            if len(batch) == 3:  # With metadata
                spectra, labels, metadata = batch
                # Count classes in this batch - metadata is a dict of lists
                for i in range(len(metadata["class"])):
                    class_name = metadata["class"][i]
                    class_counts[class_name] += 1
                    if batch_idx == 0:  # Track first batch for mixing verification
                        first_batch_classes.append(class_name)
            else:  # Without metadata
                spectra, labels = batch
            
            batch_count += 1
            sample_count += spectra.shape[0]
            
            # Log first batch details
            if batch_idx == 0:
                logger.info(f"First batch shapes - Spectra: {spectra.shape}, Labels: {labels.shape}")
                if len(batch) == 3:
                    unique_classes_first_batch = set(first_batch_classes)
                    logger.info(f"First batch contains classes: {unique_classes_first_batch}")
            
            # Stop after a reasonable number of batches for stress test
            if batch_count >= 10:
                break
                
    except Exception as e:
        logger.error(f"Error during data loading: {e}")
        sys.exit(1)
    
    end_time = time.time()
    
    # Report results
    logger.info("=== STRESS TEST RESULTS ===")
    logger.info(f"Processed {batch_count} batches, {sample_count} samples")
    logger.info(f"Time elapsed: {end_time - start_time:.2f} seconds")
    logger.info(f"Throughput: {sample_count / (end_time - start_time):.1f} samples/sec")
    
    if class_counts:
        logger.info("Class distribution in processed samples:")
        for class_name, count in class_counts.items():
            percentage = (count / sample_count) * 100 if sample_count > 0 else 0
            logger.info(f"  {class_name}: {count} samples ({percentage:.1f}%)")
        
        # Verify class mixing (should see multiple classes early if files > 1 and classes > 1)
        if len(files) > 1 and len(class_to_idx) > 1:
            unique_classes_seen = set(class_counts.keys()) - {cls for cls, count in class_counts.items() if count == 0}
            if len(unique_classes_seen) > 1:
                logger.info("✓ Class mixing verified: Multiple classes appear in early batches")
            else:
                logger.warning("⚠ Limited class mixing: Only one class seen in early batches")
    
    logger.info("Stress test completed successfully")


def main():
    parser = argparse.ArgumentParser(
        description="Generate large TFRecord files and stress-test the data loader"
    )
    parser.add_argument("--out", default="data", help="Output directory for TFRecord files")
    parser.add_argument("--num-files", type=int, default=2, help="Number of TFRecord files to generate")
    parser.add_argument("--samples-per-file", type=int, default=5000, 
                       help="Number of samples per file (minimum 5000)")
    parser.add_argument("--height", type=int, default=64, help="Height of spectra arrays")
    parser.add_argument("--width", type=int, default=128, help="Width of spectra arrays")
    parser.add_argument("--classes", default="A,B", help="Comma-separated list of class names")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducible generation")
    parser.add_argument("--num-workers", type=int, default=0, help="Number of DataLoader workers")
    parser.add_argument("--batch-size", type=int, default=32, help="Batch size for DataLoader")
    parser.add_argument("--interleave-block", type=int, default=1, help="Interleave block length")
    parser.add_argument("--shuffle-buffer", type=int, default=10000, help="Shuffle buffer size")
    parser.add_argument("--allow-list", help="Path to JSON allow-list file for filtering")
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.samples_per_file < 5000:
        logger.error("samples-per-file must be at least 5000")
        sys.exit(1)
    
    classes = [cls.strip() for cls in args.classes.split(",")]
    if not classes:
        logger.error("At least one class must be specified")
        sys.exit(1)
    
    output_dir = Path(args.out)
    
    # Generate test data
    logger.info("Generating test TFRecord files...")
    test_data = generate_test_data(
        output_dir=output_dir,
        num_files=args.num_files,
        samples_per_file=args.samples_per_file,
        height=args.height,
        width=args.width,
        classes=classes,
        seed=args.seed
    )
    
    logger.info(f"Generated {len(test_data['files'])} files with {test_data['total_samples']} total samples")
    
    # Run stress test
    stress_test_loader(
        files=test_data["files"],
        class_to_idx=test_data["class_to_idx"],
        batch_size=args.batch_size,
        num_workers=args.num_workers,
        shuffle_buffer=args.shuffle_buffer,
        interleave_block=args.interleave_block,
        allow_list_path=args.allow_list
    )


if __name__ == "__main__":
    main()
